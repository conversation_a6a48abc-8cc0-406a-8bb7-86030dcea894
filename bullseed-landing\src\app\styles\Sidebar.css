/* Sidebar Styles */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  display: none;
}

.sidebar {
  position: fixed;
  top: 0;
  left: -280px;
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 999;
  transition: left 0.3s ease;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(20px);
}

.sidebar.sidebar-open {
  left: 0;
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-logo-img {
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.sidebar-logo-text {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: none;
}

.sidebar-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu-item {
  margin-bottom: 4px;
}

.sidebar-menu-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  font-weight: 500;
  position: relative;
}

.sidebar-menu-link:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
  border-left-color: rgba(0, 212, 170, 0.3);
}

.sidebar-menu-link.active {
  background: rgba(0, 212, 170, 0.1);
  color: #00d4aa;
  border-left-color: #00d4aa;
}

.sidebar-menu-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-menu-label {
  font-size: 14px;
  flex: 1;
}

/* Menu Item Tags */
.sidebar-menu-tag {
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 3px 8px;
  border-radius: 12px;
  margin-left: auto;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.sidebar-menu-tag.crypto {
  background: linear-gradient(135deg, #f7931a, #e6820e);
  color: white;
  box-shadow: 0 2px 8px rgba(247, 147, 26, 0.3);
}

.sidebar-menu-tag.earning {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
}

.sidebar-menu-tag.new {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.sidebar-menu-tag.hot {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* Tag hover effects */
.sidebar-menu-link:hover .sidebar-menu-tag {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-support {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(0, 212, 170, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-support:hover {
  background: rgba(0, 212, 170, 0.15);
}

.sidebar-support-icon {
  width: 32px;
  height: 32px;
  background: rgba(0, 212, 170, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00d4aa;
}

.sidebar-support-text {
  flex: 1;
}

.sidebar-support-title {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin-bottom: 2px;
}

.sidebar-support-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .sidebar-overlay {
    display: block;
  }
  
  .sidebar-close {
    display: block;
  }
  
  .sidebar {
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100vw;
    left: -100vw;
  }
  
  .sidebar.sidebar-open {
    left: 0;
  }
  
  .sidebar-header {
    padding: 20px 16px;
  }
  
  .sidebar-menu-link {
    padding: 16px 20px;
    font-size: 16px;
  }
  
  .sidebar-menu-icon {
    width: 24px;
    height: 24px;
  }

  .sidebar-menu-tag {
    font-size: 9px;
    padding: 2px 6px;
  }

  .sidebar-footer {
    padding: 16px;
  }
}

/* Animation for menu items */
.sidebar-menu-item {
  animation: slideInLeft 0.3s ease-out;
  animation-fill-mode: both;
}

.sidebar-menu-item:nth-child(1) { animation-delay: 0.1s; }
.sidebar-menu-item:nth-child(2) { animation-delay: 0.15s; }
.sidebar-menu-item:nth-child(3) { animation-delay: 0.2s; }
.sidebar-menu-item:nth-child(4) { animation-delay: 0.25s; }
.sidebar-menu-item:nth-child(5) { animation-delay: 0.3s; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Scrollbar for sidebar */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
